<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Lihat RAB - Client Portal Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
        }
        
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 224px;
            background: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            margin-bottom: 1rem;
        }
        
        .nav-item {
            list-style: none;
            margin: 0.25rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 0.35rem;
        }
        
        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        #content-wrapper {
            flex: 1;
            margin-left: 224px;
            display: flex;
            flex-direction: column;
        }
        
        .topbar {
            background: white;
            padding: 1rem 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-fluid {
            padding: 1.5rem;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 400;
            color: #5a5c69;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            font-size: 0.85rem;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            margin: 0 0.5rem;
            color: #6c757d;
        }
        
        .card {
            background: white;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            display: flex;
            align-items: center;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.75rem;
        }
        
        .col-xl-3, .col-md-6 {
            padding: 0.75rem;
            flex: 0 0 25%;
        }
        
        .stat-card {
            border-left: 0.25rem solid;
            padding: 1.25rem;
        }
        
        .stat-card.primary { border-left-color: #4e73df; }
        .stat-card.success { border-left-color: #1cc88a; }
        .stat-card.danger { border-left-color: #e74a3b; }
        .stat-card.warning { border-left-color: #f6c23e; }
        
        .stat-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-text {
            flex: 1;
        }
        
        .stat-label {
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 0.25rem;
        }
        
        .stat-label.primary { color: #4e73df; }
        .stat-label.success { color: #1cc88a; }
        .stat-label.danger { color: #e74a3b; }
        .stat-label.warning { color: #f6c23e; }
        
        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .stat-icon {
            font-size: 2rem;
            color: #dddfeb;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            border-top: 1px solid #e3e6f0;
            text-align: left;
        }
        
        .table thead th {
            background: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 700;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 700;
        }
        
        .badge-success {
            background: #1cc88a;
            color: white;
        }
        
        .badge-warning {
            background: #f6c23e;
            color: white;
        }
        
        .badge-danger {
            background: #e74a3b;
            color: white;
        }
        
        .badge-secondary {
            background: #858796;
            color: white;
        }
        
        .btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .btn-primary {
            background: #4e73df;
            border-color: #4e73df;
            color: white;
        }
        
        .btn-outline-secondary {
            background: transparent;
            border-color: #6c757d;
            color: #6c757d;
        }
        
        .footer {
            background: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e3e6f0;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-xl-3, .col-md-6 {
                flex: 0 0 50%;
            }
        }
        
        @media (max-width: 576px) {
            .col-xl-3, .col-md-6 {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
                <div style="font-size: 1.2rem; font-weight: bold;">Client Portal</div>
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    <span style="margin-right: 0.5rem;">📊</span>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    <span style="margin-right: 0.5rem;">📋</span>
                    <span>Progress Proyek</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    <span style="margin-right: 0.5rem;">🖼️</span>
                    <span>File Desain</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    <span style="margin-right: 0.5rem;">✏️</span>
                    <span>Ajukan Revisi</span>
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#rab">
                    <span style="margin-right: 0.5rem;">🧮</span>
                    <span>Lihat RAB</span>
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar -->
            <nav class="topbar">
                <div>
                    <button style="background: none; border: none; font-size: 1.2rem;">☰</button>
                </div>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="position: relative;">
                        🔔
                        <span style="position: absolute; top: -5px; right: -5px; background: #e74a3b; color: white; border-radius: 50%; width: 1rem; height: 1rem; font-size: 0.7rem; display: flex; align-items: center; justify-content: center;">3</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>John Doe</span>
                        <div style="width: 2rem; height: 2rem; border-radius: 50%; background: #dddfeb;"></div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Rencana Anggaran Biaya (RAB)</h1>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Dashboard</span>
                        <span class="breadcrumb-item">Lihat RAB</span>
                    </div>
                </div>
                
                <!-- Info Alert -->
                <div class="alert alert-info">
                    <div style="margin-right: 1rem; font-size: 2rem;">ℹ️</div>
                    <div>
                        <h5 style="margin-bottom: 0.5rem;">Informasi RAB</h5>
                        <p style="margin: 0;">
                            Berikut adalah daftar Rencana Anggaran Biaya (RAB) untuk proyek Anda. 
                            Anda dapat melihat detail breakdown biaya dan mengunduh RAB dalam format PDF.
                        </p>
                    </div>
                </div>
                
                <!-- RAB Statistics -->
                <div class="row">
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card primary">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label primary">Total RAB</div>
                                    <div class="stat-number">5</div>
                                </div>
                                <div class="stat-icon">🧮</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card success">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label success">RAB Disetujui</div>
                                    <div class="stat-number">3</div>
                                </div>
                                <div class="stat-icon">✅</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card danger">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label danger">RAB Ditolak</div>
                                    <div class="stat-number">1</div>
                                </div>
                                <div class="stat-icon">❌</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card warning">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label warning">RAB Draft</div>
                                    <div class="stat-number">1</div>
                                </div>
                                <div class="stat-icon">📝</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- RAB Table -->
                <div class="card">
                    <div class="card-header">
                        📋 Daftar RAB Proyek
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Nama RAB</th>
                                    <th>Total Biaya</th>
                                    <th>Tanggal Dibuat</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>RAB Pondasi & Struktur</td>
                                    <td>Rp 150,000,000</td>
                                    <td>05 Jan 2025</td>
                                    <td><span class="badge badge-success">Approved</span></td>
                                    <td>
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Detail</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>RAB Instalasi Listrik</td>
                                    <td>Rp 75,000,000</td>
                                    <td>10 Jan 2025</td>
                                    <td><span class="badge badge-success">Approved</span></td>
                                    <td>
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Detail</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>RAB Finishing Interior</td>
                                    <td>Rp 120,000,000</td>
                                    <td>12 Jan 2025</td>
                                    <td><span class="badge badge-warning">Draft</span></td>
                                    <td>
                                        <a href="#" class="btn btn-outline-secondary">Detail</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>RAB Landscape</td>
                                    <td>Rp 50,000,000</td>
                                    <td>15 Jan 2025</td>
                                    <td><span class="badge badge-danger">Rejected</span></td>
                                    <td>
                                        <a href="#" class="btn btn-outline-secondary">Detail</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>RAB Keseluruhan Proyek</td>
                                    <td>Rp 395,000,000</td>
                                    <td>16 Jan 2025</td>
                                    <td><span class="badge badge-success">Approved</span></td>
                                    <td>
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Detail</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                <span>Copyright © Client Portal 2025</span>
            </footer>
        </div>
    </div>
</body>
</html>
