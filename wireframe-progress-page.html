<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Progress Proyek - Client Portal Wireframe</title>
    <link rel="stylesheet" href="wireframe-styles.css">
    <style>
        /* Include the same base styles from the main wireframe */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
        }
        
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 224px;
            background: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            margin-bottom: 1rem;
        }
        
        .nav-item {
            list-style: none;
            margin: 0.25rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 0.35rem;
        }
        
        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        #content-wrapper {
            flex: 1;
            margin-left: 224px;
            display: flex;
            flex-direction: column;
        }
        
        .topbar {
            background: white;
            padding: 1rem 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-fluid {
            padding: 1.5rem;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 400;
            color: #5a5c69;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            font-size: 0.85rem;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            margin: 0 0.5rem;
            color: #6c757d;
        }
        
        .card {
            background: white;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .progress {
            height: 1.5rem;
            background: #f8f9fc;
            border-radius: 0.35rem;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-bar {
            background: #1cc88a;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            font-weight: 700;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.75rem;
        }
        
        .col-md-8, .col-md-4 {
            padding: 0.75rem;
        }
        
        .col-md-8 { flex: 0 0 66.666667%; }
        .col-md-4 { flex: 0 0 33.333333%; }
        
        .stats-grid {
            display: flex;
            text-align: center;
            gap: 1rem;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .stat-number.success { color: #1cc88a; }
        .stat-number.warning { color: #f6c23e; }
        .stat-number.danger { color: #e74a3b; }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            border-top: 1px solid #e3e6f0;
            text-align: left;
        }
        
        .table thead th {
            background: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 700;
        }
        
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 700;
        }
        
        .badge-success {
            background: #1cc88a;
            color: white;
        }
        
        .badge-warning {
            background: #f6c23e;
            color: white;
        }
        
        .badge-danger {
            background: #e74a3b;
            color: white;
        }
        
        .footer {
            background: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e3e6f0;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-md-8, .col-md-4 {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
                <div style="font-size: 1.2rem; font-weight: bold;">Client Portal</div>
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    <span style="margin-right: 0.5rem;">📊</span>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#progress">
                    <span style="margin-right: 0.5rem;">📋</span>
                    <span>Progress Proyek</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    <span style="margin-right: 0.5rem;">🖼️</span>
                    <span>File Desain</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    <span style="margin-right: 0.5rem;">✏️</span>
                    <span>Ajukan Revisi</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    <span style="margin-right: 0.5rem;">🧮</span>
                    <span>Lihat RAB</span>
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar -->
            <nav class="topbar">
                <div>
                    <button style="background: none; border: none; font-size: 1.2rem;">☰</button>
                </div>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="position: relative;">
                        🔔
                        <span style="position: absolute; top: -5px; right: -5px; background: #e74a3b; color: white; border-radius: 50%; width: 1rem; height: 1rem; font-size: 0.7rem; display: flex; align-items: center; justify-content: center;">3</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>John Doe</span>
                        <div style="width: 2rem; height: 2rem; border-radius: 50%; background: #dddfeb;"></div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Progress Proyek Anda</h1>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Dashboard</span>
                        <span class="breadcrumb-item">Progress Proyek</span>
                    </div>
                </div>
                
                <!-- Info Alert -->
                <div class="alert alert-info">
                    <span style="margin-right: 0.5rem;">ℹ️</span>
                    Timeline menampilkan progress tugas proyek Anda yang telah disetujui.
                </div>
                
                <!-- Progress Summary -->
                <div class="card">
                    <div class="card-header">
                        📈 Ringkasan Progress
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 style="margin-bottom: 1rem;">Progress Keseluruhan: 66.7%</h5>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 66.7%">66.7%</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-number success">8</div>
                                        <small style="color: #858796;">Selesai</small>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number warning">4</div>
                                        <small style="color: #858796;">Proses</small>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number danger">0</div>
                                        <small style="color: #858796;">Batal</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Timeline Table -->
                <div class="card">
                    <div class="card-header">
                        📋 Timeline Proyek Anda
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Nama Kegiatan</th>
                                    <th>Tanggal Mulai</th>
                                    <th>Tanggal Selesai</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Desain Pondasi</td>
                                    <td>01 Jan 2025</td>
                                    <td>05 Jan 2025</td>
                                    <td><span class="badge badge-success">Selesai</span></td>
                                    <td>100%</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Struktur Lantai 1</td>
                                    <td>06 Jan 2025</td>
                                    <td>15 Jan 2025</td>
                                    <td><span class="badge badge-success">Selesai</span></td>
                                    <td>100%</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Instalasi Listrik</td>
                                    <td>16 Jan 2025</td>
                                    <td>25 Jan 2025</td>
                                    <td><span class="badge badge-warning">Proses</span></td>
                                    <td>60%</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>Finishing Interior</td>
                                    <td>26 Jan 2025</td>
                                    <td>10 Feb 2025</td>
                                    <td><span class="badge badge-warning">Proses</span></td>
                                    <td>0%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                <span>Copyright © Client Portal 2025</span>
            </footer>
        </div>
    </div>
</body>
</html>
