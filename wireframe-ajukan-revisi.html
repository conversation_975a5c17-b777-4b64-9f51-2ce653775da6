<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Ajukan Revisi - Client Portal Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
        }
        
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 224px;
            background: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            margin-bottom: 1rem;
        }
        
        .nav-item {
            list-style: none;
            margin: 0.25rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 0.35rem;
        }
        
        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        #content-wrapper {
            flex: 1;
            margin-left: 224px;
            display: flex;
            flex-direction: column;
        }
        
        .topbar {
            background: white;
            padding: 1rem 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-fluid {
            padding: 1.5rem;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 400;
            color: #5a5c69;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            font-size: 0.85rem;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            margin: 0 0.5rem;
            color: #6c757d;
        }
        
        .card {
            background: white;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            display: flex;
            align-items: center;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .form-control {
            width: 100%;
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
            background: white;
            font-size: 0.875rem;
        }
        
        .form-control:focus {
            border-color: #4e73df;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        
        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        
        .file-upload-area {
            border: 2px dashed #d1d3e2;
            border-radius: 0.35rem;
            padding: 2rem;
            text-align: center;
            background: #f8f9fc;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
        }
        
        .file-upload-area:hover {
            border-color: #4e73df;
            background: #f0f3ff;
        }
        
        .file-upload-icon {
            font-size: 3rem;
            color: #dddfeb;
            margin-bottom: 1rem;
        }
        
        .btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 400;
            text-align: center;
            transition: all 0.15s ease-in-out;
        }
        
        .btn-primary {
            background: #4e73df;
            border-color: #4e73df;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2e59d9;
            border-color: #2653d4;
        }
        
        .btn-secondary {
            background: #858796;
            border-color: #858796;
            color: white;
        }
        
        .btn-lg {
            padding: 0.5rem 1rem;
            font-size: 1.25rem;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.75rem;
        }
        
        .col-lg-8 {
            padding: 0.75rem;
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
            margin: 0 auto;
        }
        
        .footer {
            background: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e3e6f0;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-lg-8 {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
                <div style="font-size: 1.2rem; font-weight: bold;">Client Portal</div>
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    <span style="margin-right: 0.5rem;">📊</span>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    <span style="margin-right: 0.5rem;">📋</span>
                    <span>Progress Proyek</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    <span style="margin-right: 0.5rem;">🖼️</span>
                    <span>File Desain</span>
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#revision">
                    <span style="margin-right: 0.5rem;">✏️</span>
                    <span>Ajukan Revisi</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    <span style="margin-right: 0.5rem;">🧮</span>
                    <span>Lihat RAB</span>
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar -->
            <nav class="topbar">
                <div>
                    <button style="background: none; border: none; font-size: 1.2rem;">☰</button>
                </div>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="position: relative;">
                        🔔
                        <span style="position: absolute; top: -5px; right: -5px; background: #e74a3b; color: white; border-radius: 50%; width: 1rem; height: 1rem; font-size: 0.7rem; display: flex; align-items: center; justify-content: center;">3</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>John Doe</span>
                        <div style="width: 2rem; height: 2rem; border-radius: 50%; background: #dddfeb;"></div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Ajukan Revisi</h1>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Dashboard</span>
                        <span class="breadcrumb-item">Ajukan Revisi</span>
                    </div>
                </div>
                
                <!-- Quota Info Alert -->
                <div class="alert alert-info">
                    <div style="margin-right: 1rem; font-size: 2rem;">ℹ️</div>
                    <div>
                        <h5 style="margin-bottom: 0.5rem;">Quota Revisi Harian</h5>
                        <p style="margin: 0;">
                            Anda telah menggunakan <strong>1</strong> dari <strong>4</strong> quota revisi hari ini.
                            Sisa quota: <strong>3</strong> revisi.
                        </p>
                    </div>
                </div>
                
                <!-- Form Ajukan Revisi -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                ✏️ Form Ajukan Revisi
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="form-group">
                                        <label class="form-label">Jenis Item</label>
                                        <select class="form-control">
                                            <option value="">Pilih Jenis Item</option>
                                            <option value="file">File Desain</option>
                                            <option value="tugas">Tugas Proyek</option>
                                        </select>
                                        <div class="form-text">Pilih jenis item yang ingin direvisi</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Item yang Direvisi</label>
                                        <select class="form-control">
                                            <option value="">Pilih jenis item terlebih dahulu</option>
                                        </select>
                                        <div class="form-text">Pilih item spesifik yang ingin direvisi</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Jenis Revisi</label>
                                        <select class="form-control">
                                            <option value="">Pilih Jenis Revisi</option>
                                            <option value="minor">Minor (Perubahan Kecil)</option>
                                            <option value="major">Major (Perubahan Besar)</option>
                                            <option value="critical">Critical (Perubahan Mendesak)</option>
                                        </select>
                                        <div class="form-text">Tentukan tingkat urgensi revisi</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Deskripsi Revisi</label>
                                        <textarea class="form-control" placeholder="Jelaskan secara detail revisi yang diinginkan..."></textarea>
                                        <div class="form-text">Berikan penjelasan yang jelas dan detail tentang revisi yang diinginkan</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">File Pendukung (Opsional)</label>
                                        <div class="file-upload-area">
                                            <div class="file-upload-icon">📎</div>
                                            <h5>Klik untuk upload file</h5>
                                            <p style="color: #6c757d; margin: 0;">
                                                Drag & drop file atau klik untuk browse<br>
                                                <small>Format: PDF, JPG, PNG, DOC (Max: 10MB)</small>
                                            </p>
                                        </div>
                                        <div class="form-text">Upload file pendukung seperti sketsa, referensi, atau dokumen lainnya</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">Prioritas</label>
                                        <select class="form-control">
                                            <option value="normal">Normal</option>
                                            <option value="high">Tinggi</option>
                                            <option value="urgent">Mendesak</option>
                                        </select>
                                        <div class="form-text">Tentukan prioritas penanganan revisi</div>
                                    </div>
                                    
                                    <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            📤 Ajukan Revisi
                                        </button>
                                        <button type="reset" class="btn btn-secondary btn-lg">
                                            🔄 Reset Form
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                <span>Copyright © Client Portal 2025</span>
            </footer>
        </div>
    </div>
</body>
</html>
