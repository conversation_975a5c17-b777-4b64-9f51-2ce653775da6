<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>File Desain - Client Portal Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
        }
        
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 224px;
            background: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .sidebar-brand {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            margin-bottom: 1rem;
        }
        
        .nav-item {
            list-style: none;
            margin: 0.25rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-radius: 0.35rem;
        }
        
        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        #content-wrapper {
            flex: 1;
            margin-left: 224px;
            display: flex;
            flex-direction: column;
        }
        
        .topbar {
            background: white;
            padding: 1rem 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .container-fluid {
            padding: 1.5rem;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 400;
            color: #5a5c69;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            font-size: 0.85rem;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            margin: 0 0.5rem;
            color: #6c757d;
        }
        
        .card {
            background: white;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.75rem;
        }
        
        .col-xl-6, .col-md-6 {
            padding: 0.75rem;
            flex: 0 0 50%;
        }
        
        .stat-card {
            border-left: 0.25rem solid;
            padding: 1.25rem;
        }
        
        .stat-card.success { border-left-color: #1cc88a; }
        .stat-card.info { border-left-color: #36b9cc; }
        
        .stat-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-text {
            flex: 1;
        }
        
        .stat-label {
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 0.25rem;
        }
        
        .stat-label.success { color: #1cc88a; }
        .stat-label.info { color: #36b9cc; }
        
        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .stat-icon {
            font-size: 2rem;
            color: #dddfeb;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .file-card {
            background: white;
            border: 1px solid #e3e6f0;
            border-radius: 0.35rem;
            overflow: hidden;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .file-preview {
            height: 200px;
            background: #f8f9fc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #dddfeb;
        }
        
        .file-info {
            padding: 1rem;
        }
        
        .file-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #5a5c69;
        }
        
        .file-meta {
            font-size: 0.85rem;
            color: #858796;
            margin-bottom: 0.5rem;
        }
        
        .file-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .btn-primary {
            background: #4e73df;
            border-color: #4e73df;
            color: white;
        }
        
        .btn-outline-secondary {
            background: transparent;
            border-color: #6c757d;
            color: #6c757d;
        }
        
        .footer {
            background: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e3e6f0;
            margin-top: auto;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-xl-6, .col-md-6 {
                flex: 0 0 100%;
            }
            
            .file-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
                <div style="font-size: 1.2rem; font-weight: bold;">Client Portal</div>
            </div>
            
            <li class="nav-item">
                <a class="nav-link" href="#dashboard">
                    <span style="margin-right: 0.5rem;">📊</span>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    <span style="margin-right: 0.5rem;">📋</span>
                    <span>Progress Proyek</span>
                </a>
            </li>
            
            <li class="nav-item active">
                <a class="nav-link" href="#files">
                    <span style="margin-right: 0.5rem;">🖼️</span>
                    <span>File Desain</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    <span style="margin-right: 0.5rem;">✏️</span>
                    <span>Ajukan Revisi</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    <span style="margin-right: 0.5rem;">🧮</span>
                    <span>Lihat RAB</span>
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar -->
            <nav class="topbar">
                <div>
                    <button style="background: none; border: none; font-size: 1.2rem;">☰</button>
                </div>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="position: relative;">
                        🔔
                        <span style="position: absolute; top: -5px; right: -5px; background: #e74a3b; color: white; border-radius: 50%; width: 1rem; height: 1rem; font-size: 0.7rem; display: flex; align-items: center; justify-content: center;">3</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span>John Doe</span>
                        <div style="width: 2rem; height: 2rem; border-radius: 50%; background: #dddfeb;"></div>
                    </div>
                </div>
            </nav>
            
            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">File Desain Disetujui</h1>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Dashboard</span>
                        <span class="breadcrumb-item">File Desain</span>
                    </div>
                </div>
                
                <!-- Info Alert -->
                <div class="alert alert-success">
                    <span style="margin-right: 0.5rem;">✅</span>
                    <strong>Informasi:</strong> Halaman ini menampilkan file desain yang sudah melalui proses verifikasi dan disetujui untuk proyek Anda.
                </div>
                
                <!-- File Statistics -->
                <div class="row">
                    <div class="col-xl-6 col-md-6">
                        <div class="card stat-card success">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label success">Total File Disetujui</div>
                                    <div class="stat-number">15</div>
                                </div>
                                <div class="stat-icon">🖼️</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-6 col-md-6">
                        <div class="card stat-card info">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label info">File Baru (7 Hari Terakhir)</div>
                                    <div class="stat-number">3</div>
                                </div>
                                <div class="stat-icon">⏰</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- File Gallery -->
                <div class="card">
                    <div class="card-header">
                        🖼️ Galeri File Desain
                    </div>
                    <div class="card-body">
                        <div class="file-grid">
                            <div class="file-card">
                                <div class="file-preview">📄</div>
                                <div class="file-info">
                                    <div class="file-name">blueprint_v2.pdf</div>
                                    <div class="file-meta">
                                        Ukuran: 2.5 MB<br>
                                        Disetujui: 14 Jan 2025
                                    </div>
                                    <div class="file-actions">
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Preview</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="file-card">
                                <div class="file-preview">🖼️</div>
                                <div class="file-info">
                                    <div class="file-name">denah_lantai1.jpg</div>
                                    <div class="file-meta">
                                        Ukuran: 1.8 MB<br>
                                        Disetujui: 13 Jan 2025
                                    </div>
                                    <div class="file-actions">
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Preview</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="file-card">
                                <div class="file-preview">📐</div>
                                <div class="file-info">
                                    <div class="file-name">struktur_pondasi.dwg</div>
                                    <div class="file-meta">
                                        Ukuran: 3.2 MB<br>
                                        Disetujui: 12 Jan 2025
                                    </div>
                                    <div class="file-actions">
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Preview</a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="file-card">
                                <div class="file-preview">📊</div>
                                <div class="file-info">
                                    <div class="file-name">3d_render.png</div>
                                    <div class="file-meta">
                                        Ukuran: 4.1 MB<br>
                                        Disetujui: 11 Jan 2025
                                    </div>
                                    <div class="file-actions">
                                        <a href="#" class="btn btn-primary">Download</a>
                                        <a href="#" class="btn btn-outline-secondary">Preview</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="footer">
                <span>Copyright © Client Portal 2025</span>
            </footer>
        </div>
    </div>
</body>
</html>
