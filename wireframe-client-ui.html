<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Client Portal - Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fc;
            color: #5a5c69;
        }
        
        /* Layout Structure */
        #wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            width: 224px;
            background: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-brand {
            padding: 1.5rem 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.15);
        }
        
        .sidebar-brand-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .sidebar-brand-text {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .nav-item {
            list-style: none;
            margin: 0.25rem 0;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.15s ease-in-out;
        }
        
        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        
        .nav-link i {
            margin-right: 0.5rem;
            width: 1rem;
        }
        
        .sidebar-divider {
            border: 0;
            height: 1px;
            background: rgba(255,255,255,0.15);
            margin: 1rem 0;
        }
        
        .sidebar-heading {
            padding: 0.75rem 1.5rem;
            font-size: 0.65rem;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.1rem;
            color: rgba(255,255,255,0.4);
        }
        
        /* Content Wrapper */
        #content-wrapper {
            flex: 1;
            margin-left: 224px;
            display: flex;
            flex-direction: column;
        }
        
        /* Topbar */
        .topbar {
            background: white;
            padding: 1rem 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .topbar-left {
            display: flex;
            align-items: center;
        }
        
        .topbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .notification-icon {
            position: relative;
            padding: 0.5rem;
            color: #5a5c69;
        }
        
        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74a3b;
            color: white;
            border-radius: 50%;
            width: 1.2rem;
            height: 1.2rem;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #dddfeb;
        }
        
        /* Main Content */
        .container-fluid {
            padding: 1.5rem;
            flex: 1;
        }
        
        .page-heading {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 400;
            color: #5a5c69;
        }
        
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
            font-size: 0.85rem;
        }
        
        .breadcrumb-item {
            display: inline;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
            margin: 0 0.5rem;
            color: #6c757d;
        }
        
        /* Cards */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.75rem;
        }
        
        .col-xl-3, .col-md-6, .col-lg-8, .col-lg-4, .col-lg-12 {
            padding: 0.75rem;
        }
        
        .col-xl-3, .col-md-6 { flex: 0 0 25%; }
        .col-lg-8 { flex: 0 0 66.666667%; }
        .col-lg-4 { flex: 0 0 33.333333%; }
        .col-lg-12 { flex: 0 0 100%; }
        
        .card {
            background: white;
            border-radius: 0.35rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 0.75rem 1.25rem;
            background: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        /* Stat Cards */
        .stat-card {
            border-left: 0.25rem solid;
            padding: 1.25rem;
        }
        
        .stat-card.primary { border-left-color: #4e73df; }
        .stat-card.success { border-left-color: #1cc88a; }
        .stat-card.warning { border-left-color: #f6c23e; }
        .stat-card.info { border-left-color: #36b9cc; }
        
        .stat-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stat-text {
            flex: 1;
        }
        
        .stat-label {
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
            margin-bottom: 0.25rem;
        }
        
        .stat-label.primary { color: #4e73df; }
        .stat-label.success { color: #1cc88a; }
        .stat-label.warning { color: #f6c23e; }
        .stat-label.info { color: #36b9cc; }
        
        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: #5a5c69;
        }
        
        .stat-icon {
            font-size: 2rem;
            color: #dddfeb;
        }
        
        /* Progress Bar */
        .progress {
            height: 1rem;
            background: #f8f9fc;
            border-radius: 0.35rem;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-bar {
            background: #1cc88a;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            font-weight: 700;
        }
        
        /* Quick Access */
        .list-group-item {
            padding: 1rem;
            border: 1px solid #e3e6f0;
            border-top: 0;
            display: block;
            text-decoration: none;
            color: #5a5c69;
        }
        
        .list-group-item:first-child {
            border-top: 1px solid #e3e6f0;
            border-top-left-radius: 0.35rem;
            border-top-right-radius: 0.35rem;
        }
        
        .list-group-item:last-child {
            border-bottom-left-radius: 0.35rem;
            border-bottom-right-radius: 0.35rem;
        }
        
        .list-group-item:hover {
            background: #f8f9fc;
        }
        
        /* Timeline */
        .timeline-item {
            display: flex;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .timeline-icon {
            margin-right: 1rem;
            color: #4e73df;
        }
        
        .timeline-content h6 {
            margin-bottom: 0.25rem;
            font-weight: 600;
        }
        
        .timeline-date {
            font-size: 0.8rem;
            color: #858796;
        }
        
        /* Footer */
        .footer {
            background: white;
            padding: 1rem;
            text-align: center;
            border-top: 1px solid #e3e6f0;
            margin-top: auto;
        }
        
        /* Alerts */
        .alert {
            padding: 0.75rem 1.25rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        /* Tables */
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            border-top: 1px solid #e3e6f0;
            text-align: left;
        }
        
        .table thead th {
            background: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 700;
        }
        
        /* Forms */
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .form-control {
            width: 100%;
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
            background: white;
        }
        
        .btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid transparent;
            border-radius: 0.35rem;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #4e73df;
            border-color: #4e73df;
            color: white;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            #content-wrapper {
                margin-left: 0;
            }
            
            .col-xl-3, .col-md-6 {
                flex: 0 0 50%;
            }
            
            .col-lg-8, .col-lg-4 {
                flex: 0 0 100%;
            }
        }
        
        @media (max-width: 576px) {
            .col-xl-3, .col-md-6 {
                flex: 0 0 100%;
            }
        }
    </style>
</head>
<body>
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="sidebar">
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">👤</div>
                <div class="sidebar-brand-text">Client Portal</div>
            </div>
            
            <hr class="sidebar-divider">
            
            <li class="nav-item active">
                <a class="nav-link" href="#dashboard">
                    <i>📊</i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <hr class="sidebar-divider">
            
            <div class="sidebar-heading">Proyek</div>
            
            <li class="nav-item">
                <a class="nav-link" href="#progress">
                    <i>📋</i>
                    <span>Progress Proyek</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#files">
                    <i>🖼️</i>
                    <span>File Desain</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#revision">
                    <i>✏️</i>
                    <span>Ajukan Revisi</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="#rab">
                    <i>🧮</i>
                    <span>Lihat RAB</span>
                </a>
            </li>
            
            <hr class="sidebar-divider">
            
            <li class="nav-item">
                <a class="nav-link" href="#logout">
                    <i>🚪</i>
                    <span>Keluar</span>
                </a>
            </li>
        </ul>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Topbar -->
            <nav class="topbar">
                <div class="topbar-left">
                    <button>☰</button>
                </div>
                <div class="topbar-right">
                    <div class="notification-icon">
                        🔔
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="user-info">
                        <span>John Doe</span>
                        <div class="user-avatar"></div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid">
                <!-- Page Heading -->
                <div class="page-heading">
                    <h1 class="page-title">Dashboard Client</h1>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">Selamat datang, John Doe!</span>
                    </div>
                </div>

                <!-- Stats Cards Row -->
                <div class="row">
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card primary">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label primary">Total Tugas Disetujui</div>
                                    <div class="stat-number">12</div>
                                </div>
                                <div class="stat-icon">📋</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card success">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label success">Tugas Selesai</div>
                                    <div class="stat-number">8</div>
                                </div>
                                <div class="stat-icon">✅</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card warning">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label warning">Tugas Dalam Proses</div>
                                    <div class="stat-number">4</div>
                                </div>
                                <div class="stat-icon">⏰</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card info">
                            <div class="stat-content">
                                <div class="stat-text">
                                    <div class="stat-label info">File Desain Disetujui</div>
                                    <div class="stat-number">15</div>
                                </div>
                                <div class="stat-icon">🖼️</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress and Quick Access Row -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                📈 Progress Proyek
                            </div>
                            <div class="card-body">
                                <h5>Progress Keseluruhan: 66.7%</h5>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 66.7%">66.7% Selesai</div>
                                </div>
                                <p style="text-align: center; color: #858796; margin-top: 1rem;">
                                    8 dari 12 tugas telah selesai
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                ⚡ Akses Cepat
                            </div>
                            <div class="card-body" style="padding: 0;">
                                <a href="#progress" class="list-group-item">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <h6 style="margin: 0;">📊 Lihat Progress Proyek</h6>
                                        <small style="color: #858796;">Timeline</small>
                                    </div>
                                    <p style="margin: 0.5rem 0 0 0; color: #858796; font-size: 0.9rem;">
                                        Lihat progress proyek dalam bentuk Gantt chart
                                    </p>
                                </a>
                                <a href="#files" class="list-group-item">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <h6 style="margin: 0;">🖼️ File Desain</h6>
                                        <small style="color: #858796;">Approved</small>
                                    </div>
                                    <p style="margin: 0.5rem 0 0 0; color: #858796; font-size: 0.9rem;">
                                        Lihat dan download file desain yang disetujui
                                    </p>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                🕒 Aktivitas Terbaru
                            </div>
                            <div class="card-body">
                                <div class="timeline-item">
                                    <div class="timeline-icon">📋</div>
                                    <div class="timeline-content">
                                        <h6>Tugas "Desain Lantai 1" disetujui</h6>
                                        <div class="timeline-date">15 Jan 2025 14:30</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon">🖼️</div>
                                    <div class="timeline-content">
                                        <h6>File "blueprint_v2.pdf" disetujui</h6>
                                        <div class="timeline-date">14 Jan 2025 09:15</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon">📋</div>
                                    <div class="timeline-content">
                                        <h6>Tugas "Struktur Pondasi" disetujui</h6>
                                        <div class="timeline-date">13 Jan 2025 16:45</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="footer">
                <span>Copyright © Client Portal 2025</span>
            </footer>
        </div>
    </div>
</body>
</html>
